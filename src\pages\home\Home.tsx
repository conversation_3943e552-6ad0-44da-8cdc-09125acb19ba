import React, { useState } from "react";
import {
  MdDashboard,
  MdCheckCircle,
  MdCancel,
  MdOutlineAccountCircle,
  MdArrowDropDown,
  MdArrowDropUp,
} from "react-icons/md";
import { AiOutlinePlus } from "react-icons/ai";
import { FaWallet } from "react-icons/fa";
import { TbCurrencyRupee } from "react-icons/tb";
import {
  BsFillJournalBookmarkFill,
  BsFillAirplaneEnginesFill,
  BsViewList,
  BsFillCircleFill,
} from "react-icons/bs";

import { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer } from "recharts";

const chartData = [
  { month: "SEP", value1: 100, value2: 110 },
  { month: "OCT", value1: 120, value2: 130 },
  { month: "NOV", value1: 108, value2: 115 },
  { month: "DEC", value1: 95, value2: 100 },
  { month: "JAN", value1: 110, value2: 120 },
  { month: "FEB", value1: 115, value2: 125 },
];

const NavbarItem = ({ children, active }: { children: React.ReactNode; active: boolean }) => (
  <li
    className={`cursor-pointer px-5 py-3 text-sm font-semibold flex items-center gap-2 hover:text-[#20C997] ${active ? "text-white border-b-4 border-[#20C997]" : "text-slate-300 border-b-4 border-transparent"
      }`}
  >
    {children}
  </li>
);

export default function Home() {
  const [navOpen, setNavOpen] = useState(false);

  return (
    <div className="min-h-screen bg-slate-50 text-slate-800 p-4 md:p-8 font-sans select-none">
      {/* Navbar */}
      <nav className="bg-[#1D3A4B] rounded-lg shadow-lg px-4 md:px-12 py-3 flex items-center justify-between">
        <img
         src="/brandLogo.svg" 
         alt="brand-logo" 
          height={150}
          width={150}
         />

        <div className="md:hidden">
          <button
            onClick={() => setNavOpen((v) => !v)}
            className="text-white text-3xl focus:outline-none"
            aria-label="Toggle menu"
          >
            {navOpen ? <MdArrowDropUp /> : <MdArrowDropDown />}
          </button>
        </div>

        <ul
          className={`md:flex items-center gap-6 text-white font-semibold transition-all duration-300 ease-in-out md:static absolute md:top-auto md:left-auto md:bg-transparent bg-[#1D3A4B] w-full left-0 md:w-auto ${navOpen ? "top-[56px] p-4 rounded-b-lg shadow-lg z-20" : "top-[-300px]"
            }`}
        >
          <NavbarItem active>Home</NavbarItem>
          <NavbarItem active={false}>Insights</NavbarItem>
          <NavbarItem active={false}>Notifications</NavbarItem>
          <NavbarItem active={false}>Profile</NavbarItem>
        </ul>

        <button
          type="button"
          className="bg-gradient-to-r from-[#20C997] to-[#27E2B6] px-5 py-2 rounded-lg text-white font-semibold hover:brightness-105"
        >
          <AiOutlinePlus className="inline mr-1 -mb-0.5" />
          Add Expense
        </button>
      </nav>

      {/* Top Pending/Approved/Rejected */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-5">
        <div className="bg-white rounded-xl p-5 flex items-center gap-4 shadow-sm">
          <MdDashboard className="text-orange-400 text-3xl" />
          <div>
            <p className="text-sm font-semibold text-slate-400">Pending</p>
            <p className="text-2xl font-bold text-orange-400">4</p>
          </div>
        </div>
        <div className="bg-white rounded-xl p-5 flex items-center gap-4 shadow-sm">
          <MdCheckCircle className="text-green-400 text-3xl" />
          <div>
            <p className="text-sm font-semibold text-slate-400">Approved</p>
            <p className="text-2xl font-bold text-green-400">15</p>
          </div>
        </div>
        <div className="bg-white rounded-xl p-5 flex items-center gap-4 shadow-sm">
          <MdCancel className="text-red-400 text-3xl" />
          <div>
            <p className="text-sm font-semibold text-slate-400">Rejected</p>
            <p className="text-2xl font-bold text-red-400">6</p>
          </div>
        </div>
      </div>

      {/* Info cards + chart container */}
      <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-5">
        {/* Left summary and details */}
        <div className="space-y-5">
          {/* Usage limit */}
          <div className="bg-white rounded-xl shadow-sm p-5">
            <div className="flex justify-between text-sm text-slate-400 font-semibold mb-2">
              <span>Usage Limit</span>
              <span>$350.60 / $50000</span>
            </div>
            <div className="w-full bg-slate-300 rounded-full h-2 overflow-hidden">
              <div
                className="bg-gradient-to-r from-cyan-500 to-teal-400 h-2 rounded-full"
                style={{ width: `${(350.6 / 50000) * 100}%` }}
              />
            </div>
          </div>

          {/* Latest Reimbursement Request */}
          <div className="bg-white rounded-xl shadow-sm p-5">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-lg font-semibold text-slate-800">
                Latest Reimbursement Request{" "}
                <span className="text-slate-400 font-normal text-sm">Travel to client location 12/12/2022</span>
              </h2>
              <span className="bg-[#1D3A4B] text-white px-3 py-1 rounded-full text-xs whitespace-nowrap select-none">
                Submitted
              </span>
            </div>
            <div className="flex items-center gap-6 my-2 font-semibold text-slate-700 text-sm">
              <span className="flex items-center gap-1 text-cyan-600">
                <TbCurrencyRupee size={16} /> 8000.00
              </span>
              <span className="flex items-center gap-1 text-teal-600">
                <FaWallet size={18} /> Multiple Expenses
              </span>
            </div>

            <div className="bg-slate-100 rounded-lg p-3 flex gap-3 text-slate-600 text-xl border border-slate-300 select-none" style={{ maxWidth: 280 }}>
              <button
                className="flex items-center gap-2 rounded px-2 py-1 hover:bg-slate-200"
                title="Airplane"
              >
                <BsFillAirplaneEnginesFill />
              </button>
              <button
                className="flex items-center gap-2 rounded px-2 py-1 hover:bg-slate-200"
                title="Multiple Expenses"
              >
                <BsViewList />
              </button>
            </div>

            <div className="mt-5 flex bg-[#1D3A4B] rounded-lg text-white text-xs font-semibold overflow-hidden" style={{ maxWidth: 560 }}>
              <button className="flex-1 px-5 py-3 bg-[#20C997] rounded-l-lg flex items-center gap-2 cursor-default">
                <MdCheckCircle className="text-lg" /> Manager
              </button>
              <button className="flex-1 px-5 py-3 border-l border-[#20C997] flex items-center gap-2 opacity-70 cursor-default">
                <MdCheckCircle className="text-lg" /> Finance Dept
              </button>
              <button className="flex-1 px-5 py-3 border-l border-[#20C997] flex items-center gap-2 opacity-70 cursor-default rounded-r-lg">
                <MdCheckCircle className="text-lg" /> Payment Release
              </button>
            </div>
          </div>
        </div>

        {/* Right chart and info */}
        <div className="space-y-5">
          <div className="bg-white rounded-lg shadow-sm p-5 flex flex-col sm:flex-row gap-4 sm:gap-10 select-none">
            <div className="flex items-center gap-4 sm:flex-1">
              <div className="bg-[#1D3A4B] p-3 rounded-xl text-white">
                <BsFillJournalBookmarkFill size={32} />
              </div>
              <div>
                <span className="text-slate-500 text-xs font-semibold uppercase tracking-wider">
                  Common Category Expenditure
                </span>
                <p className="font-semibold text-slate-800 mt-1">Travel & Transportation</p>
              </div>
            </div>

            <div className="flex items-center gap-4 sm:flex-1">
              <div className="bg-[#1D3A4B] p-3 rounded-xl text-white flex items-center justify-center" style={{ width: 48, height: 48 }}>
                <MdArrowDropDown size={28} />
              </div>
              <div>
                <span className="text-slate-500 text-xs font-semibold uppercase tracking-wider">
                  Average Approval Time
                </span>
                <p className="font-semibold text-slate-800 mt-1 text-sm">
                  Your claims are usually approved in <span className="text-[#20C997]">1 to 2 days</span>
                </p>
              </div>
            </div>
          </div>

          {/* Historical Trend Chart */}
          <div className="bg-white rounded-lg shadow-lg p-5 select-none">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-semibold text-slate-900">Historical Trend</h3>
              <div className="flex items-center gap-3 text-slate-400 text-xs cursor-pointer select-none">
                <label className="flex items-center gap-1 cursor-pointer">
                  <input type="checkbox" className="h-3 w-3" />
                  This Year
                </label>
                <MdDashboard />
              </div>
            </div>
            <p className="text-3xl font-extrabold text-slate-900 mb-1">$37.5K</p>
            <p className="text-xs text-green-500 font-semibold mb-3">Total Spent ↑ 2.45%</p>
            <div className="flex gap-2 items-center text-sm text-green-600 font-semibold mb-5">
              <BsFillCircleFill />
              <span>On track</span>
            </div>
            <div style={{ width: "100%", height: 180 }}>
              <ResponsiveContainer>
                <LineChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                  <XAxis dataKey="month" tick={{ fill: "#94a3b8" }} />
                  <YAxis hide />
                  <Tooltip
                    contentStyle={{ borderRadius: "8px", backgroundColor: "#f0fdfa", color: "#0f766e" }}
                    formatter={(value) => [`$${value}`, ""]}
                  />
                  <Line
                    type="monotone"
                    dataKey="value1"
                    stroke="#14b8a6"
                    strokeWidth={3}
                    dot={{ r: 6, stroke: "#14b8a6", strokeWidth: 3 }}
                    activeDot={{ r: 8 }}
                  />
                  <Line type="monotone" dataKey="value2" stroke="#20C997" strokeWidth={3} dot={false} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>

      {/* All Activity Table */}
      <div className="mt-10 rounded-lg bg-white shadow-sm p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-3">
          <h3 className="font-semibold text-slate-900 select-none">All Activity</h3>
          <div className="flex gap-3 items-center flex-wrap">
            <input
              type="date"
              className="p-2 border border-slate-300 rounded-lg text-sm text-slate-600"
              placeholder="Date"
            />
            <button className="px-4 py-1 rounded-lg bg-slate-200 text-sm text-slate-600 cursor-default select-none">All</button>
            <button className="px-4 py-1 rounded-lg bg-orange-400 text-white text-sm font-semibold select-none">Pending</button>
            <button className="px-4 py-1 rounded-lg bg-slate-200 text-sm text-slate-600 cursor-pointer">Approved</button>
            <button className="px-4 py-1 rounded-lg bg-slate-200 text-sm text-slate-600 cursor-pointer">Rejected</button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full table-fixed border-collapse border border-slate-200 text-sm">
            <thead>
              <tr className="bg-slate-100 text-slate-600 select-none">
                <th className="border border-slate-300 px-4 py-2 w-[20%] text-left">Expense Report</th>
                <th className="border border-slate-300 px-4 py-2 text-left">Description</th>
                <th className="border border-slate-300 px-4 py-2 w-[12%] text-left">Category</th>
                <th className="border border-slate-300 px-4 py-2 w-[12%] text-left">Submission Date</th>
                <th className="border border-slate-300 px-4 py-2 w-[12%] text-right">Amount</th>
                <th className="border border-slate-300 px-4 py-2 w-[12%] text-right">Usage Limit</th>
                <th className="border border-slate-300 px-4 py-2 w-[12%] text-center">Approvals</th>
                <th className="border border-slate-300 px-4 py-2 w-[8%] text-right">Actions</th>
              </tr>
            </thead>
            <tbody className="text-slate-700">
              {[1, 2].map((row) => (
                <tr key={row} className="hover:bg-slate-50 cursor-default">
                  <td className="border border-slate-200 px-4 py-3 font-semibold">Travel to client location</td>
                  <td className="border border-slate-200 px-4 py-3 text-slate-500 truncate max-w-[150px]" title="Exploring the world through travel adventures.">
                    Exploring the world through travel adventures.
                  </td>
                  <td className="border border-slate-200 px-4 py-3 flex items-center gap-1 justify-start text-center">
                    <BsViewList className="text-slate-600" />
                    <span className="text-xs font-semibold text-slate-600">Multiple Expenses</span>
                  </td>
                  <td className="border border-slate-200 px-4 py-3 text-center text-sm text-slate-600">2024-07-20</td>
                  <td className="border border-slate-200 px-4 py-3 text-right font-bold text-slate-800">$4000.00</td>
                  <td className="border border-slate-200 px-4 py-3 text-right text-slate-500">$50,000.00</td>
                  <td className="border border-slate-200 px-4 py-3 text-center flex justify-center gap-1">
                    <MdCheckCircle className="text-green-500" />
                    <MdOutlineAccountCircle className="text-slate-400 opacity-70" />
                    <MdOutlineAccountCircle className="text-slate-400 opacity-70" />
                  </td>
                  <td className="border border-slate-200 px-4 py-3 text-right text-slate-600 font-semibold cursor-pointer hover:text-[#20C997]">
                    View
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Active Disputes Section */}
      <div className="mt-12 select-none">
        <h3 className="font-semibold text-slate-900 mb-3">Active Disputes</h3>
        <div className="flex items-center gap-3 text-slate-600 text-xs font-semibold">
          {["12 months", "30 days", "7 days", "24 hours"].map((t, i) => (
            <button
              key={t}
              className={`px-4 py-1 rounded-lg ${i === 0 ? "bg-slate-900 text-white" : "bg-slate-200"
                } cursor-pointer select-none`}
            >
              {t}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}